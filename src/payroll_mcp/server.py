"""
FastMCP server for Payroll Grid API integration.

This module implements the main MCP server using FastMCP with SSE transport,
providing a single 'payroll' tool that interfaces with the external
payroll grid API via JSON-RPC 2.0.
"""

import asyncio
import signal
import sys
from typing import Any, Dict, List, Optional

import structlog
from fastmcp import FastMCP
from fastmcp.tools.tool import <PERSON>lR<PERSON>ult
from mcp.types import TextContent
from pydantic import ValidationError

from .client import PayrollApiClient, PayrollApiError
from .config import Config, ConfigurationError, load_config
from .logging import get_logger, log_request_context, setup_logging
from .models import PayrollParameters, PayrollSuccessResponse, PayrollErrorResponse

# Global configuration and logger
config: Optional[Config] = None
logger: Optional[structlog.BoundLogger] = None

# Create FastMCP server instance
mcp = FastMCP("Payroll Grid MCP Server")


def _create_success_content_blocks(
    response: PayrollSuccessResponse,
) -> List[TextContent]:
    """
    Create human-readable content blocks for successful payroll responses.

    Args:
        response: The structured success response

    Returns:
        List of TextContent blocks with formatted summaries
    """
    content_blocks = []

    # Main summary
    summary_text = f"""# Payroll Query Results

{response.result_summary}

## Query Parameters
- Type: {response.query_parameters.get('type', 'N/A')}
- Farming Year: {response.query_parameters.get('farming_year', 'N/A')}
- Date Range: {response.query_parameters.get('payroll_from_date', 'N/A')} to {response.query_parameters.get('payroll_to_date', 'N/A')}
- Page: {response.pagination.get('page', 1)} (showing {response.pagination.get('rows', 30)} rows per page)

## Summary Statistics"""

    # Add summary stats if available
    if response.payroll_data.summary_stats:
        stats = response.payroll_data.summary_stats
        summary_text += f"""
- Total Area: {stats.get('total_area', 'N/A')}
- Total Rent: {stats.get('total_rent', 'N/A')}
- Total Paid: {stats.get('total_paid', 'N/A')}
- Total Unpaid: {stats.get('total_unpaid', 'N/A')}
- Total Overpaid: {stats.get('total_overpaid', 'N/A')}"""

    details_text = f"""
## Data Overview
The response contains {response.payroll_data.returned_rows} detailed payroll records with comprehensive owner information, financial data, and contract details. Each record includes:

- Owner identification (names, EGN/EIK, contact info)
- Land and area information (total area, cultivated area, personal use)
- Financial data (rent amounts, payments, outstanding balances)
- Contract details (farming IDs, contract numbers, plot information)
- Payment history (money and natura payments)
# The structure content field descriptions:
    * owner_names :  Собственик 
    * egn_eik :  ЕГН/ЕИК 
    * rent_place_name :  Място за получаване на рента 
    * all_owner_area :  Притежавана площ(дка) 
    * pu_area :  Площ за лично ползване(дка) 
    * owner_area :  Използвана площ(дка) 
    * cultivated_area :  Обработваема   площ 
    * renta_txt :  рента в пари 
    * renta_nat_with_type :  рента в натура 
    * charged_renta_txt :  начислена рента в пари 
    * charged_renta_nat_with_type :  начислена рента в натура по тип 
    * paid_renta_txt :  платена рента в пари 
    * paid_renta_by_txt :  платена рента  чрез 
    * paid_renta_nat_with_type :  платена рента в натура 
    * paid_renta_nat_by :  платена рента в натура чрез 
    * paid_renta_nat_by_detailed :  платена рента в натура чрез - детайлно 
    * unpaid_renta_txt :  остатък в пари 
    * unpaid_renta_nat :  остатък в натура 
    * unpaid_renta_nat_unit_value :  остатък в пари (ед. ст.) 
    * over_paid_txt :  надплатена в пари 
    * over_paid_nat :  надплатена в натура 
    * children :  списък с наследници 
Use the structured_content for programmatic access to all detailed data."""

    content_blocks.append(TextContent(type="text", text=details_text))

    return content_blocks


def _create_error_content_blocks(
    error_response: PayrollErrorResponse,
) -> List[TextContent]:
    """
    Create human-readable content blocks for error responses.

    Args:
        error_response: The structured error response

    Returns:
        List of TextContent blocks with formatted error information
    """
    error_text = f"""# Payroll Query Error

**Error Type:** {error_response.error_type}
**Message:** {error_response.error_message}
**Timestamp:** {error_response.timestamp.isoformat()}

## Error Details"""

    if error_response.error_details:
        for key, value in error_response.error_details.items():
            error_text += f"\n- **{key}:** {value}"

    if error_response.suggested_actions:
        error_text += "\n\n## Suggested Actions"
        for i, action in enumerate(error_response.suggested_actions, 1):
            error_text += f"\n{i}. {action}"

    if error_response.request_context:
        error_text += "\n\n## Request Context"
        for key, value in error_response.request_context.items():
            error_text += f"\n- **{key}:** {value}"

    return [TextContent(type="text", text=error_text)]


@mcp.tool()
async def payroll(
    # Core Parameters (with defaults)
    type: str = "owners",
    farming_year: int = 16,
    # Date Range Parameters (with defaults)
    payroll_from_date: str = "2024-10-01",
    payroll_to_date: str = "2025-09-30",
    # Location and Administrative Filters (with defaults)
    payroll_ekate: Optional[List[str]] = None,
    payroll_farming: Optional[List[str]] = None,
    # Owner Type and Identification Filters (with defaults)
    owner_type: str = "0,1",
    owner_names: str = "",
    egn: str = "",
    eik: str = "",
    company_name: str = "",
    # Advanced Owner Filters (with defaults)
    owner_egns: Optional[List[str]] = None,
    company_eiks: Optional[List[str]] = None,
    # Representative Filters (with defaults)
    rep_names: str = "",
    rep_egn: str = "",
    rep_rent_place: str = "",
    # Location Filters (with defaults)
    rent_place: str = "",
    # Heritor Filters (with defaults)
    heritor_names: str = "",
    heritor_egn: str = "",
    # Pagination and Sorting (optional)
    page: int = 1,
    rows: int = 30,
    sort: str = "owner_names",
    order: str = "asc",
) -> ToolResult:
    """
    Retrieve payroll grid data from the external API.

    This tool interfaces with the external payroll grid API to fetch
    payroll data based on the provided parameters. It supports filtering
    by owner information, date ranges, locations, and various other criteria.

    Args:
        payroll_from_date: Start date for payroll period (YYYY-MM-DD)
        payroll_to_date: End date for payroll period (YYYY-MM-DD)
        payroll_ekate: EKATE administrative territorial codes filter
        payroll_farming: Farming operations/entities filter
        owner_type: Owner type filter - "0" (companies), "1" (individuals), "0,1" (both)
        owner_names: Text search filter for owner names (individuals only)
        egn: Individual identification number (EGN) filter
        eik: Company identification number (EIK) filter
        company_name: Company name text search filter
        owner_egns: Multiple individual identification numbers for exact matching
        company_eiks: Multiple company identification numbers for exact matching
        rep_names: Representative names text search filter
        rep_egn: Representative identification number filter
        rep_rent_place: Representative rent place/location filter
        rent_place: Owner rent place/location filter
        heritor_names: Heritor names text search filter
        heritor_egn: Heritor identification number filter
        page: Page number for pagination (default: 1)
        rows: Number of rows per page (default: 30)
        sort: Sort field (default: "owner_names")
        order: Sort order "asc" or "desc" (default: "asc")

    Returns:
        JSON string containing the payroll data response

    Raises:
        Various exceptions for validation errors, API errors, etc.
    """
    global config, logger

    if not config or not logger:
        raise RuntimeError("Server not properly initialized")

    request_id = f"payroll-{asyncio.current_task().get_name() if asyncio.current_task() else 'unknown'}"

    # Create request context for logging
    request_context = log_request_context(
        "payroll",
        request_id,
        type=type,
        farming_year=farming_year,
        page=page,
        rows=rows,
    )

    logger.info("Payroll tool called", **request_context)

    try:
        # Handle list parameters with defaults
        if payroll_ekate is None:
            payroll_ekate = [""]
        if payroll_farming is None:
            payroll_farming = [""]
        if owner_egns is None:
            owner_egns = []
        if company_eiks is None:
            company_eiks = []

        # Validate parameters using Pydantic model
        parameters = PayrollParameters(
            type=type,
            farming_year=farming_year,
            payroll_from_date=payroll_from_date,
            payroll_to_date=payroll_to_date,
            payroll_ekate=payroll_ekate,
            payroll_farming=payroll_farming,
            owner_type=owner_type,
            owner_names=owner_names,
            egn=egn,
            eik=eik,
            company_name=company_name,
            owner_egns=owner_egns,
            company_eiks=company_eiks,
            rep_names=rep_names,
            rep_egn=rep_egn,
            rep_rent_place=rep_rent_place,
            rent_place=rent_place,
            heritor_names=heritor_names,
            heritor_egn=heritor_egn,
        )

        logger.debug(
            "Parameters validated successfully",
            **request_context,
            params=parameters.model_dump(),
            param_count=len(parameters.to_api_params()),
        )

        # Call external API
        async with PayrollApiClient(config) as api_client:
            response = await api_client.read_payroll_grid(
                parameters=parameters, page=page, rows=rows, sort=sort, order=order
            )

        # Create comprehensive success response
        success_response = PayrollSuccessResponse(
            payroll_data=response,
            query_parameters={
                "type": type,
                "farming_year": farming_year,
                "payroll_from_date": payroll_from_date,
                "payroll_to_date": payroll_to_date,
                "owner_type": owner_type,
                "owner_names": owner_names,
                "egn": egn,
                "eik": eik,
                "company_name": company_name,
            },
            pagination={
                "page": page,
                "rows": rows,
                "sort": sort,
                "order": order,
            },
        )

        # Create FastMCP ToolResult with content blocks and structured data
        content_blocks = _create_success_content_blocks(success_response)
        result = ToolResult(
            # content=content_blocks,
            structured_content=success_response.model_dump(),
        )

        logger.info(
            "Payroll tool completed successfully",
            **request_context,
            total_rows=response.total,
            returned_rows=len(response.rows),
            footer_items=len(response.footer),
        )

        return result

    except ValidationError as e:
        error_response = PayrollErrorResponse(
            error_type="validation",
            error_message=f"Parameter validation failed: {e}",
            error_details={
                "validation_errors": str(e),
                "error_count": len(e.errors()) if hasattr(e, "errors") else 1,
            },
            request_context=request_context,
            suggested_actions=[
                "Check parameter types and formats",
                "Ensure required parameters are provided",
                "Verify date formats (YYYY-MM-DD)",
                "Check that numeric parameters are within valid ranges",
            ],
        )

        logger.error(
            "Parameter validation error",
            **request_context,
            error=error_response.error_message,
        )

        content_blocks = _create_error_content_blocks(error_response)
        result = ToolResult(
            content=content_blocks,
            structured_content=error_response.model_dump(),
        )
        return result

    except PayrollApiError as e:
        error_response = PayrollErrorResponse(
            error_type="api",
            error_message=f"Payroll API error: {e}",
            error_details={
                "api_error": str(e),
                "error_class": type(e).__name__,
            },
            request_context=request_context,
            suggested_actions=[
                "Check API endpoint availability",
                "Verify authentication credentials",
                "Check network connectivity",
                "Try again with different parameters",
            ],
        )

        logger.error(
            "Payroll API error", **request_context, error=error_response.error_message
        )

        content_blocks = _create_error_content_blocks(error_response)
        result = ToolResult(
            content=content_blocks,
            structured_content=error_response.model_dump(),
        )
        return result

    except Exception as e:
        error_response = PayrollErrorResponse(
            error_type="unexpected",
            error_message=f"Unexpected error: {e}",
            error_details={
                "exception_type": type(e).__name__,
                "exception_message": str(e),
                "traceback_available": True,
            },
            request_context=request_context,
            suggested_actions=[
                "Check server logs for detailed error information",
                "Verify all system dependencies are available",
                "Try the request again",
                "Contact system administrator if problem persists",
            ],
        )

        logger.error(
            "Unexpected error in payroll tool",
            **request_context,
            error=error_response.error_message,
            error_type=type(e).__name__,
        )

        content_blocks = _create_error_content_blocks(error_response)
        result = ToolResult(
            content=content_blocks,
            structured_content=error_response.model_dump(),
        )
        return result


def create_server() -> FastMCP:
    """
    Create and configure the FastMCP server.

    Returns:
        Configured FastMCP server instance
    """
    return mcp


async def startup() -> None:
    """Initialize the server with configuration and logging."""
    global config, logger

    try:
        # Load configuration
        config = load_config()

        # Setup logging
        setup_logging(config)
        logger = get_logger(__name__, component="mcp_server")

        logger.info(
            "Payroll MCP server starting up",
            version="0.1.0",
            api_url=config.payroll_endpoint_url,
            host=config.mcp_host,
            port=config.mcp_port,
            log_level=config.log_level,
        )

    except ConfigurationError as e:
        print(f"❌ Configuration error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"❌ Startup error: {e}", file=sys.stderr)
        sys.exit(1)


async def shutdown() -> None:
    """Clean shutdown of the server."""
    global logger

    if logger:
        logger.info("Payroll MCP server shutting down")


def setup_signal_handlers() -> None:
    """Setup signal handlers for graceful shutdown."""

    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        asyncio.create_task(shutdown())
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main() -> None:
    """Main entry point for the server."""
    # Setup signal handlers
    setup_signal_handlers()

    # Initialize server
    asyncio.run(startup())

    # Run the MCP server with SSE transport
    try:
        mcp.run(transport="sse", host=config.mcp_host, port=config.mcp_port)
    except Exception as e:
        if logger:
            logger.error("Server failed to start", error=str(e))
        else:
            print(f"❌ Server failed to start: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
